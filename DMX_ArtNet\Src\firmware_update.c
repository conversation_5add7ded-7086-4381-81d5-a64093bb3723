#include "firmware_update.h"
#include "platform.h"

// Simple string copy function since string.h may not be available
static void fw_strcpy(char* dest, const char* src) {
    while ((*dest++ = *src++));
}

// Simple string length function
static int fw_strlen(const char* str) {
    int len = 0;
    while (str[len]) len++;
    return len;
}

// Simple memory clear function
static void fw_memclr(void* ptr, uint32_t size) {
    uint8_t* p = (uint8_t*)ptr;
    for (uint32_t i = 0; i < size; i++) {
        p[i] = 0;
    }
}

// Simple memory copy function
static void fw_memcpy(void* dest, const void* src, uint32_t size) {
    uint8_t* d = (uint8_t*)dest;
    const uint8_t* s = (const uint8_t*)src;
    for (uint32_t i = 0; i < size; i++) {
        d[i] = s[i];
    }
}

// Simple memory set function
static void fw_memset(void* ptr, uint8_t value, uint32_t size) {
    uint8_t* p = (uint8_t*)ptr;
    for (uint32_t i = 0; i < size; i++) {
        p[i] = value;
    }
}

// Global firmware update context
static firmware_update_context_t fw_context;

// Buffer for flash programming (must be aligned to flash page size)
static uint8_t flash_buffer[FLASH_PAGE_SIZE] __attribute__((aligned(8)));

void FirmwareUpdate_Init(void) {
    fw_memclr(&fw_context, sizeof(firmware_update_context_t));
    fw_context.state = FIRMWARE_UPDATE_STATE_IDLE;
    fw_context.buffer = flash_buffer;
    fw_context.buffer_size = FLASH_PAGE_SIZE;
}

firmware_update_result_t FirmwareUpdate_Start(void) {
    if (fw_context.state != FIRMWARE_UPDATE_STATE_IDLE) {
        return FIRMWARE_UPDATE_ERROR_IN_PROGRESS;
    }

    // Initialize context
    fw_memclr(&fw_context, sizeof(firmware_update_context_t));
    fw_context.state = FIRMWARE_UPDATE_STATE_RECEIVING;
    fw_context.buffer = flash_buffer;
    fw_context.buffer_size = FLASH_PAGE_SIZE;
    fw_context.flash_address = FIRMWARE_START_ADDRESS;
    fw_context.checksum = 0;
    fw_context.header_received = 0;

    return FIRMWARE_UPDATE_SUCCESS;
}

firmware_update_result_t FirmwareUpdate_ProcessData(const uint8_t* data, uint32_t length) {
    if (fw_context.state != FIRMWARE_UPDATE_STATE_RECEIVING) {
        return FIRMWARE_UPDATE_ERROR_IN_PROGRESS;
    }

    if (!data || length == 0) {
        return FIRMWARE_UPDATE_ERROR_INVALID_FILE;
    }

    // Check total size limit
    if (fw_context.received_size + length > MAX_FIRMWARE_SIZE) {
        fw_strcpy(fw_context.error_message, "Firmware file too large");
        fw_context.state = FIRMWARE_UPDATE_STATE_ERROR;
        return FIRMWARE_UPDATE_ERROR_FILE_TOO_LARGE;
    }

    // Process ELF header if not yet received
    if (!fw_context.header_received && fw_context.received_size + length >= sizeof(elf_header_t)) {
        uint32_t header_bytes_needed = sizeof(elf_header_t) - fw_context.received_size;
        
        // Copy header data
        fw_memcpy(((uint8_t*)&fw_context.elf_header) + fw_context.received_size,
               data, header_bytes_needed);
        
        // Validate ELF header
        firmware_update_result_t result = validate_elf_header(&fw_context.elf_header);
        if (result != FIRMWARE_UPDATE_SUCCESS) {
            fw_context.state = FIRMWARE_UPDATE_STATE_ERROR;
            return result;
        }
        
        fw_context.header_received = 1;
    }

    // Process data in chunks
    uint32_t data_offset = 0;
    while (data_offset < length) {
        uint32_t chunk_size = length - data_offset;
        uint32_t buffer_space = fw_context.buffer_size - fw_context.buffer_pos;
        
        if (chunk_size > buffer_space) {
            chunk_size = buffer_space;
        }

        // Copy data to buffer
        fw_memcpy(fw_context.buffer + fw_context.buffer_pos, data + data_offset, chunk_size);
        fw_context.buffer_pos += chunk_size;
        data_offset += chunk_size;

        // Update checksum
        fw_context.checksum = calculate_checksum(data + data_offset - chunk_size, chunk_size);

        // If buffer is full, program it to flash
        if (fw_context.buffer_pos >= fw_context.buffer_size) {
            firmware_update_result_t result = program_flash_page(
                fw_context.flash_address, 
                fw_context.buffer, 
                fw_context.buffer_pos
            );
            
            if (result != FIRMWARE_UPDATE_SUCCESS) {
                fw_context.state = FIRMWARE_UPDATE_STATE_ERROR;
                return result;
            }

            fw_context.flash_address += fw_context.buffer_pos;
            fw_context.buffer_pos = 0;
        }
    }

    fw_context.received_size += length;
    return FIRMWARE_UPDATE_SUCCESS;
}

firmware_update_result_t FirmwareUpdate_Finalize(void) {
    if (fw_context.state != FIRMWARE_UPDATE_STATE_RECEIVING) {
        return FIRMWARE_UPDATE_ERROR_IN_PROGRESS;
    }

    fw_context.state = FIRMWARE_UPDATE_STATE_VALIDATING;

    // Program remaining data in buffer
    if (fw_context.buffer_pos > 0) {
        // Pad buffer with 0xFF (flash erased state)
        fw_memset(fw_context.buffer + fw_context.buffer_pos, 0xFF,
               fw_context.buffer_size - fw_context.buffer_pos);

        firmware_update_result_t result = program_flash_page(
            fw_context.flash_address, 
            fw_context.buffer, 
            fw_context.buffer_size
        );
        
        if (result != FIRMWARE_UPDATE_SUCCESS) {
            fw_context.state = FIRMWARE_UPDATE_STATE_ERROR;
            return result;
        }
    }

    fw_context.state = FIRMWARE_UPDATE_STATE_COMPLETE;
    return FIRMWARE_UPDATE_SUCCESS;
}

void FirmwareUpdate_Abort(void) {
    fw_context.state = FIRMWARE_UPDATE_STATE_ERROR;
    fw_strcpy(fw_context.error_message, "Update aborted by user");
}

firmware_update_state_t FirmwareUpdate_GetState(void) {
    return fw_context.state;
}

uint32_t FirmwareUpdate_GetProgress(void) {
    if (fw_context.total_size == 0) {
        return 0;
    }
    return (fw_context.received_size * 100) / fw_context.total_size;
}

const char* FirmwareUpdate_GetErrorMessage(void) {
    return fw_context.error_message;
}

// Internal function implementations
static firmware_update_result_t validate_elf_header(const elf_header_t* header) {
    if (!header) {
        fw_strcpy(fw_context.error_message, "Invalid ELF header pointer");
        return FIRMWARE_UPDATE_ERROR_INVALID_FORMAT;
    }

    // Check ELF magic number
    if (header->e_ident_magic != ELF_MAGIC) {
        fw_strcpy(fw_context.error_message, "Invalid ELF magic number");
        return FIRMWARE_UPDATE_ERROR_INVALID_FORMAT;
    }

    // Check for 32-bit ELF
    if (header->e_ident_class != ELF_CLASS_32) {
        fw_strcpy(fw_context.error_message, "Only 32-bit ELF files supported");
        return FIRMWARE_UPDATE_ERROR_INVALID_FORMAT;
    }

    // Check endianness (little endian)
    if (header->e_ident_data != ELF_DATA_LSB) {
        fw_strcpy(fw_context.error_message, "Only little-endian ELF files supported");
        return FIRMWARE_UPDATE_ERROR_INVALID_FORMAT;
    }

    // Check file type (executable)
    if (header->e_type != ELF_TYPE_EXEC) {
        fw_strcpy(fw_context.error_message, "ELF file must be executable");
        return FIRMWARE_UPDATE_ERROR_INVALID_FORMAT;
    }

    // Check machine type (ARM)
    if (header->e_machine != ELF_MACHINE_ARM) {
        fw_strcpy(fw_context.error_message, "ELF file must be for ARM architecture");
        return FIRMWARE_UPDATE_ERROR_INVALID_FORMAT;
    }

    // Check entry point is in valid flash range
    if (header->e_entry < FIRMWARE_START_ADDRESS ||
        header->e_entry >= (FIRMWARE_START_ADDRESS + MAX_FIRMWARE_SIZE)) {
        fw_strcpy(fw_context.error_message, "Invalid entry point address");
        return FIRMWARE_UPDATE_ERROR_INVALID_FORMAT;
    }

    return FIRMWARE_UPDATE_SUCCESS;
}

static firmware_update_result_t program_flash_page(uint32_t address, const uint8_t* data, uint32_t length) {
    if (!data || length == 0 || length > FLASH_PAGE_SIZE) {
        fw_strcpy(fw_context.error_message, "Invalid flash programming parameters");
        return FIRMWARE_UPDATE_ERROR_FLASH_ERROR;
    }

    // Check address alignment
    if (address % FLASH_PAGE_SIZE != 0) {
        fw_strcpy(fw_context.error_message, "Flash address not page-aligned");
        return FIRMWARE_UPDATE_ERROR_FLASH_ERROR;
    }

    // Check address range
    if (address < FIRMWARE_START_ADDRESS ||
        address >= (FIRMWARE_START_ADDRESS + CONFIG_PAGES_START)) {
        fw_strcpy(fw_context.error_message, "Flash address out of range");
        return FIRMWARE_UPDATE_ERROR_FLASH_ERROR;
    }

    unlock_flash();

    // Erase page
    firmware_update_result_t result = erase_flash_pages(address, FLASH_PAGE_SIZE);
    if (result != FIRMWARE_UPDATE_SUCCESS) {
        lock_flash();
        return result;
    }

    // Program page
    FLASH->CR |= FLASH_CR_PG;
    
    volatile uint32_t* flash_ptr = (volatile uint32_t*)address;
    uint32_t* data_ptr = (uint32_t*)data;
    
    for (uint32_t i = 0; i < length / sizeof(uint32_t); i += 2) {
        flash_ptr[i] = data_ptr[i];
        flash_ptr[i + 1] = data_ptr[i + 1];
        
        // Wait for programming to complete
        while (FLASH->SR & FLASH_SR_BSY) {
            // Check for errors
            if (FLASH->SR & (FLASH_SR_PGSERR | FLASH_SR_PGAERR | FLASH_SR_WRPERR)) {
                FLASH->CR &= ~FLASH_CR_PG;
                lock_flash();
                fw_strcpy(fw_context.error_message, "Flash programming error");
                return FIRMWARE_UPDATE_ERROR_FLASH_ERROR;
            }
        }
        
        FLASH->SR &= ~FLASH_SR_EOP;
    }
    
    FLASH->CR &= ~FLASH_CR_PG;
    lock_flash();

    return FIRMWARE_UPDATE_SUCCESS;
}

static firmware_update_result_t erase_flash_pages(uint32_t start_address, uint32_t length) {
    uint32_t page_num = (start_address - FLASH_BASE_ADDRESS) / FLASH_PAGE_SIZE;
    uint32_t num_pages = (length + FLASH_PAGE_SIZE - 1) / FLASH_PAGE_SIZE;

    for (uint32_t i = 0; i < num_pages; i++) {
        // Wait for any ongoing operation
        while (FLASH->SR & FLASH_SR_BSY) {}

        // Clear error flags
        FLASH->SR = FLASH_SR_PGSERR;
        
        // Set page erase
        FLASH->CR |= FLASH_CR_PER;
        FLASH->CR &= ~FLASH_CR_PNB_Msk;
        FLASH->CR |= ((page_num + i) << FLASH_CR_PNB_Pos);

        // Start erase
        FLASH->CR |= FLASH_CR_STRT;

        // Wait for completion
        while (FLASH->SR & FLASH_SR_BSY) {}

        // Clear page erase bit
        FLASH->CR &= ~FLASH_CR_PER;

        // Check for errors
        if (FLASH->SR & FLASH_SR_PGSERR) {
            fw_strcpy(fw_context.error_message, "Flash erase error");
            return FIRMWARE_UPDATE_ERROR_FLASH_ERROR;
        }
    }

    return FIRMWARE_UPDATE_SUCCESS;
}

static uint32_t calculate_checksum(const uint8_t* data, uint32_t length) {
    uint32_t checksum = fw_context.checksum;
    for (uint32_t i = 0; i < length; i++) {
        checksum += data[i];
    }
    return checksum;
}

static void unlock_flash(void) {
    while (FLASH->SR & FLASH_SR_BSY) {}
    
    if (FLASH->CR & FLASH_CR_LOCK) {
        FLASH->KEYR = 0x45670123;
        FLASH->KEYR = 0xCDEF89AB;
    }
}

static void lock_flash(void) {
    while (FLASH->SR & FLASH_SR_BSY) {}
    FLASH->CR |= FLASH_CR_LOCK;
}

#!/usr/bin/env python3
"""
Simple script to convert HTML file to C array format for fsdata.c
"""

import sys
import os

def convert_file_to_c_array(filename):
    """Convert a file to C array format"""
    with open(filename, 'rb') as f:
        data = f.read()
    
    # Get base filename for variable names
    base_name = os.path.basename(filename).replace('.', '_').replace('-', '_')
    
    # Create the filename string (with path)
    path_name = '/' + os.path.basename(filename)
    path_bytes = path_name.encode('utf-8') + b'\x00'
    
    # Pad to align to 4 bytes
    while len(path_bytes) % 4 != 0:
        path_bytes += b'\x00'
    
    print(f"static const unsigned int dummy_align__{base_name} = 0;")
    print(f"static const unsigned char data__{base_name}[] = {{")
    
    # Print filename
    print(f"/* {path_name} ({len(path_bytes)} chars) */")
    for i in range(0, len(path_bytes), 16):
        chunk = path_bytes[i:i+16]
        hex_values = ','.join(f'0x{b:02x}' for b in chunk)
        print(f"{hex_values},")
    
    print()
    print(f"/* raw file data ({len(data)} bytes) */")
    
    # Print file data
    for i in range(0, len(data), 16):
        chunk = data[i:i+16]
        hex_values = ','.join(f'0x{b:02x}' for b in chunk)
        if i + 16 < len(data):
            print(f"{hex_values},")
        else:
            print(f"{hex_values}}};")
    
    print()
    return base_name, len(path_bytes)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 convert_html.py <filename>")
        sys.exit(1)
    
    filename = sys.argv[1]
    if not os.path.exists(filename):
        print(f"File {filename} not found")
        sys.exit(1)
    
    base_name, path_len = convert_file_to_c_array(filename)
    
    print(f"// Add this to the file list:")
    print(f"const struct fsdata_file file__{base_name}[] = {{ {{")
    print(f"file__PREVIOUS_FILE,")
    print(f"data__{base_name},")
    print(f"data__{base_name} + {path_len},")
    print(f"sizeof(data__{base_name}) - {path_len},")
    print(f"0,")
    print(f"}}}};")

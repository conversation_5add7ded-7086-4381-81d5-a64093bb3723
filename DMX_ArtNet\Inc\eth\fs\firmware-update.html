<html>

<head>
    <title>Steady Node</title>
    <link rel="stylesheet" type="text/css" href="style.css" />
    <style>
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background-color: #f9f9f9;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #303236;
            background-color: #f0f0f0;
        }

        .upload-area.dragover {
            border-color: #303236;
            background-color: #e8e8e8;
        }

        .file-input {
            display: none;
        }

        .upload-button {
            background-color: #303236;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }

        .upload-button:hover {
            background-color: #525252;
        }

        .progress-container {
            display: none;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: #303236;
            width: 0%;
            transition: width 0.3s ease;
        }

        .status-message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            display: none;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .file-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            display: none;
        }

        .warning-box {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }

        .warning-box h4 {
            margin-top: 0;
            color: #856404;
        }
    </style>
    <script>
        let selectedFile = null;
        let uploadInProgress = false;

        function initializeUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const uploadButton = document.getElementById('uploadButton');
            const startUpdateButton = document.getElementById('startUpdate');

            // Click to select file
            uploadArea.addEventListener('click', () => {
                if (!uploadInProgress) {
                    fileInput.click();
                }
            });

            // Drag and drop handlers
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                if (!uploadInProgress) {
                    uploadArea.classList.add('dragover');
                }
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                if (!uploadInProgress && e.dataTransfer.files.length > 0) {
                    handleFileSelection(e.dataTransfer.files[0]);
                }
            });

            // File input change
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFileSelection(e.target.files[0]);
                }
            });

            // Start update button
            startUpdateButton.addEventListener('click', startFirmwareUpdate);
        }

        function handleFileSelection(file) {
            if (!file.name.toLowerCase().endsWith('.elf')) {
                showStatus('error', 'Please select a valid .elf firmware file.');
                return;
            }

            if (file.size > 400 * 1024) { // 400KB max (leave space for config)
                showStatus('error', 'Firmware file is too large. Maximum size is 400KB.');
                return;
            }

            selectedFile = file;
            displayFileInfo(file);
            document.getElementById('startUpdate').style.display = 'inline-block';
            showStatus('success', 'Firmware file selected successfully. Click "Start Update" to proceed.');
        }

        function displayFileInfo(file) {
            const fileInfo = document.getElementById('fileInfo');
            fileInfo.innerHTML = `
                <strong>Selected File:</strong> ${file.name}<br>
                <strong>Size:</strong> ${(file.size / 1024).toFixed(2)} KB<br>
                <strong>Type:</strong> ${file.type || 'application/octet-stream'}
            `;
            fileInfo.style.display = 'block';
        }

        function showStatus(type, message) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
        }

        function updateProgress(percent) {
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressContainer.style.display = 'block';
            progressFill.style.width = percent + '%';
            progressText.textContent = `${percent}% Complete`;
        }

        async function startFirmwareUpdate() {
            if (!selectedFile) {
                showStatus('error', 'No firmware file selected.');
                return;
            }

            uploadInProgress = true;
            document.getElementById('startUpdate').disabled = true;
            document.getElementById('uploadArea').style.pointerEvents = 'none';
            document.getElementById('uploadArea').style.opacity = '0.5';

            showStatus('warning', 'Starting firmware update... Do not disconnect the device!');
            updateProgress(0);

            try {
                const formData = new FormData();
                formData.append('firmware', selectedFile);

                const response = await fetch('/firmware-upload', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    updateProgress(100);
                    showStatus('success', 'Firmware update completed successfully! Device will restart in 5 seconds...');
                    
                    // Countdown and redirect
                    let countdown = 5;
                    const countdownInterval = setInterval(() => {
                        countdown--;
                        if (countdown > 0) {
                            showStatus('success', `Firmware update completed successfully! Device will restart in ${countdown} seconds...`);
                        } else {
                            clearInterval(countdownInterval);
                            showStatus('success', 'Device restarting... Please wait...');
                            // Wait for device restart and redirect to main page
                            setTimeout(() => {
                                window.location.href = '/';
                            }, 3000);
                        }
                    }, 1000);
                } else {
                    const errorText = await response.text();
                    showStatus('error', `Firmware update failed: ${errorText}`);
                    resetUploadState();
                }
            } catch (error) {
                showStatus('error', `Upload failed: ${error.message}`);
                resetUploadState();
            }
        }

        function resetUploadState() {
            uploadInProgress = false;
            document.getElementById('startUpdate').disabled = false;
            document.getElementById('uploadArea').style.pointerEvents = 'auto';
            document.getElementById('uploadArea').style.opacity = '1';
            document.getElementById('progressContainer').style.display = 'none';
        }

        window.onload = initializeUpload;
    </script>
</head>

<body>
    <h1><i>Steady Node</i></h1>

    <div>
        <nav>
            <a href="/">Port Config</a>
            <a href="/device.html">Device Config</a>
            <a href="/firmware-update.html">Firmware Update</a>
        </nav>

        <div class="container vert">
            <h3>Firmware Update</h3>
            
            <div class="warning-box">
                <h4>⚠️ Important Warning</h4>
                <p><strong>Do not disconnect or power off the device during the firmware update process!</strong></p>
                <p>Interrupting the update may render your device inoperable and require recovery via STM32CubeProgrammer.</p>
                <p>Only upload firmware files (.elf) specifically designed for this device.</p>
            </div>

            <div id="uploadArea" class="upload-area">
                <p><strong>Click here or drag and drop your .elf firmware file</strong></p>
                <p>Maximum file size: 400KB</p>
                <button type="button" class="upload-button">Select Firmware File</button>
                <input type="file" id="fileInput" class="file-input" accept=".elf">
            </div>

            <div id="fileInfo" class="file-info"></div>

            <div id="statusMessage" class="status-message"></div>

            <div id="progressContainer" class="progress-container">
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <p id="progressText">0% Complete</p>
            </div>

            <button id="startUpdate" class="upload-button" style="display: none;">Start Firmware Update</button>
        </div>
    </div>
</body>

</html>

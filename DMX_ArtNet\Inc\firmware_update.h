#ifndef __FIRMWARE_UPDATE_H_
#define __FIRMWARE_UPDATE_H_

#include "platform.h"
#include "lwip/pbuf.h"

// Firmware update status codes
typedef enum {
    FIRMWARE_UPDATE_SUCCESS = 0,
    FIRMWARE_UPDATE_ERROR_INVALID_FILE = -1,
    F<PERSON><PERSON><PERSON>RE_UPDATE_ERROR_FILE_TOO_LARGE = -2,
    FIRMWARE_UPDATE_ERROR_INVALID_FORMAT = -3,
    FIRMWARE_UPDATE_ERROR_FLASH_ERROR = -4,
    FIRMWARE_UPDATE_ERROR_CHECKSUM_FAIL = -5,
    FIRMWARE_UPDATE_ERROR_MEMORY_ERROR = -6,
    FIRMWARE_UPDATE_ERROR_IN_PROGRESS = -7
} firmware_update_result_t;

// Firmware update state
typedef enum {
    FIRMWARE_UPDATE_STATE_IDLE = 0,
    FIRMWARE_UPDATE_STATE_RECEIVING = 1,
    FIRMWARE_UPDATE_STATE_VALIDATING = 2,
    FIR<PERSON><PERSON>RE_UPDATE_STATE_PROGRAMMING = 3,
    F<PERSON><PERSON><PERSON>RE_UPDATE_STATE_COMPLETE = 4,
    FIRMWARE_UPDATE_STATE_ERROR = 5
} firmware_update_state_t;

// Maximum firmware size (400KB to leave space for config pages)
#define MAX_FIRMWARE_SIZE (400 * 1024)

// Flash programming constants
#define FLASH_BASE_ADDRESS 0x08000000
#define FLASH_PAGE_SIZE 4096
#define FLASH_TOTAL_SIZE (512 * 1024)
#define FIRMWARE_START_ADDRESS FLASH_BASE_ADDRESS
#define CONFIG_PAGES_START (126 * FLASH_PAGE_SIZE)  // Same as in flash_ee.c

// ELF file constants
#define ELF_MAGIC 0x464C457F  // "\x7FELF" in little endian
#define ELF_CLASS_32 1
#define ELF_DATA_LSB 1
#define ELF_TYPE_EXEC 2
#define ELF_MACHINE_ARM 40

// ELF header structure (simplified)
#pragma pack(1)
typedef struct {
    uint32_t e_ident_magic;     // ELF magic number
    uint8_t  e_ident_class;     // 32-bit or 64-bit
    uint8_t  e_ident_data;      // Endianness
    uint8_t  e_ident_version;   // ELF version
    uint8_t  e_ident_osabi;     // OS/ABI
    uint8_t  e_ident_pad[8];    // Padding
    uint16_t e_type;            // Object file type
    uint16_t e_machine;         // Machine type
    uint32_t e_version;         // Object file version
    uint32_t e_entry;           // Entry point address
    uint32_t e_phoff;           // Program header offset
    uint32_t e_shoff;           // Section header offset
    uint32_t e_flags;           // Processor-specific flags
    uint16_t e_ehsize;          // ELF header size
    uint16_t e_phentsize;       // Program header entry size
    uint16_t e_phnum;           // Number of program header entries
    uint16_t e_shentsize;       // Section header entry size
    uint16_t e_shnum;           // Number of section header entries
    uint16_t e_shstrndx;        // Section header string table index
} elf_header_t;

// ELF program header structure
typedef struct {
    uint32_t p_type;            // Segment type
    uint32_t p_offset;          // Segment file offset
    uint32_t p_vaddr;           // Segment virtual address
    uint32_t p_paddr;           // Segment physical address
    uint32_t p_filesz;          // Segment size in file
    uint32_t p_memsz;           // Segment size in memory
    uint32_t p_flags;           // Segment flags
    uint32_t p_align;           // Segment alignment
} elf_program_header_t;
#pragma pack()

// Program header types
#define PT_LOAD 1

// Firmware update context
typedef struct {
    firmware_update_state_t state;
    uint32_t total_size;
    uint32_t received_size;
    uint32_t flash_address;
    uint8_t* buffer;
    uint32_t buffer_size;
    uint32_t buffer_pos;
    elf_header_t elf_header;
    uint8_t header_received;
    uint32_t checksum;
    char error_message[128];
} firmware_update_context_t;

// Function declarations
void FirmwareUpdate_Init(void);
firmware_update_result_t FirmwareUpdate_Start(void);
firmware_update_result_t FirmwareUpdate_ProcessData(const uint8_t* data, uint32_t length);
firmware_update_result_t FirmwareUpdate_Finalize(void);
void FirmwareUpdate_Abort(void);
firmware_update_state_t FirmwareUpdate_GetState(void);
uint32_t FirmwareUpdate_GetProgress(void);
const char* FirmwareUpdate_GetErrorMessage(void);

// Internal functions are implemented in firmware_update.c

#endif // __FIRMWARE_UPDATE_H_
